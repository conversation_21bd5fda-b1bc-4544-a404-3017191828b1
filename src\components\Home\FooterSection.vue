<template>
  <section id="footer" class="bg-primary text-white">
    <div class="overflow-hidden">
      <svg
        preserveAspectRatio="none"
        viewBox="0 0 1200 120"
        xmlns="http://www.w3.org/2000/svg"
        class="md:h-28 w-[125%]"
        style="fill: #ffffff;"
      >
        <path
          d="M0 0v46.29c47.79 22.2 103.59 32.17 158 28 70.36-5.37 136.33-33.31 206.8-37.5 73.84-4.36 147.54 16.88 218.2 35.26 69.27 18 138.3 24.88 209.4 13.08 36.15-6 69.85-17.84 104.45-29.34C989.49 25 1113-14.29 1200 52.47V0z"
          opacity=".25"
        />
        <path
          d="M0 0v15.81c13 21.11 27.64 41.05 47.69 56.24C99.41 111.27 165 111 224.58 91.58c31.15-10.15 60.09-26.07 89.67-39.8 40.92-19 84.73-46 130.83-49.67 36.26-2.85 70.9 9.42 98.6 31.56 31.77 25.39 62.32 62 103.63 73 40.44 10.79 81.35-6.69 119.13-24.28s75.16-39 116.92-43.05c59.73-5.85 113.28 22.88 168.9 38.84 30.2 8.66 59 6.17 87.09-7.5 22.43-10.89 48-26.93 60.65-49.24V0z"
          opacity=".5"
        />
        <path d="M0 0v5.63C149.93 59 314.09 71.32 475.83 42.57c43-7.64 84.23-20.12 127.61-26.46 59-8.63 112.48 12.24 165.56 35.4C827.93 77.22 886 95.24 951.2 90c86.53-7 172.46-45.71 248.8-84.81V0z" />
      </svg>
    </div>
    <!-- Contact Section -->
    <div class="text-center px-4 py-8">
      <h2 class="text-3xl font-bold tracking-tight">
        {{ t('footer.get_in_touch') }}
      </h2>
      <p class="mt-4 text-lg max-w-2xl mx-auto">
        {{ t('footer.get_in_touch_description') }}
      </p>
      <div class="mt-8 flex flex-col sm:flex-row justify-center gap-4">
        <!-- WhatsApp Message Box -->
        <div class="flex items-center gap-4 p-2 bg-white rounded-[10px] border border-gray-300 focus-within:border-gray-600">
          <div class="flex items-center justify-center">
            <img src="https://cdn-icons-png.flaticon.com/512/220/220236.png" alt="WhatsApp" class="h-5 w-5" />
          </div>
          <input
            required
            :placeholder="t('footer.whatsapp_message')"
            type="text"
            id="messageInput"
            v-model="whatsappMessage"
            class="bg-transparent outline-none text-black placeholder:text-gray-600 flex-grow px-2 min-w-0"
          />
          <button
            id="sendButton"
            @click="goto(`https://wa.me/55991023468?text=${whatsappMessage}`, true)"
            class="border-0 flex items-center justify-center cursor-pointer transition-all"
          >
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 664 663" class="h-5 w-5">
              <path
                d="M646.293 331.888L17.7538 17.6187L155.245 331.888M646.293 331.888L17.753 646.157L155.245 331.888M646.293 331.888L318.735 330.228L155.245 331.888"
              ></path>
              <path
                stroke-linejoin="round"
                stroke-linecap="round"
                stroke-width="33.67"
                stroke="#6c6c6c"
                d="M646.293 331.888L17.7538 17.6187L155.245 331.888M646.293 331.888L17.753 646.157L155.245 331.888M646.293 331.888L318.735 330.228L155.245 331.888"
              ></path>
            </svg>
          </button>
        </div>
      </div>
      <div class="mt-8 flex items-center justify-center gap-4">
        <a href="mailto:<EMAIL>" class="flex items-center gap-2 text-white font-bold">
          <q-icon name="mail" class="h-5 w-5" />
          <span class="text-sm">Email</span>
        </a>
        <a href="https://www.instagram.com/habilis.tech/" class="flex items-center gap-2 text-white font-bold" target="_blank" rel="noopener noreferrer">
          <img src="https://img.icons8.com/win10/512/FFFFFF/instagram-new.png" alt="Instagram" class="h-5 w-5" />
          <span class="text-sm">Instagram</span>
        </a>
      </div>
    </div>
    <div class="py-4">
      <div class="text-center">
        <p>&copy; {{ new Date().getFullYear() }} Habilis Tech. {{ t('footer.rights') }}</p>
      </div>
    </div>
  </section>
</template>

<script>
// import { useUserStore } from 'src/stores/user';
import { inject, ref } from 'vue';
import { getImageUrl } from 'src/utils/image';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n'
import { goto } from '../../utils/navigation'

export default {
  name: 'FooterSection',
  // components: { AnatomyButton },
  setup() {
    // const user = useUserStore();
    const bus = inject('bus');
    const router = useRouter();
    const { t, locale } = useI18n()

    const skills = [
      { name_en: 'Exploratory Analysis', name_pt: 'Análise Exploratória', icon: 'share' },// , icon_hovered: ShareIconSolid },
      { name_en: 'Data Visualization', name_pt: 'Visualização de Dados', icon: 'visibility' },// , icon_hovered: EyeIconSolid },
      { name_en: 'Data Story', name_pt: 'Data Story', icon: 'menu_book' },// , icon_hovered: BookOpenIconSolid },
      { name_en: 'Hypothesis Testing', name_pt: 'Testes de Hipóteses', icon: 'chat' },// , icon_hovered: ChatBubbleBottomCenterTextIconSolid },
      { name_en: 'Quantitative Survey Structure', name_pt: 'Delineamento de Pesquisa Quantitativa', icon: 'monitoring' },// , icon_hovered: ChartBarSquareIconSolid },
      { name_en: 'Correlation and Regression', name_pt: 'Correlação e Regressão', icon: 'database' },// , icon_hovered: Square3Stack3DIconSolid },
      { name_en: 'Confidence Intervals', name_pt: 'Intervalos de Confiança', icon: 'data_usage' },// , icon_hovered: ChartPieIconSolid },
      { name_en: 'Survey Methodology', name_pt: 'Metodologia de Pesquisa', icon: 'search' },// , icon_hovered: DocumentMagnifyingGlassIconSolid },
      { name_en: 'Results Interpretation', name_pt: 'Interpretação de Resultados', icon: 'trending_up' },// , icon_hovered: ArrowTrendingUpIconSolid },
      { name_en: 'Scientific Writing', name_pt: 'Escrita Científica', icon: 'science' },// , icon_hovered: BeakerIconSolid },
      { name_en: 'Data Analysis Reports', name_pt: 'Relatórios de Análise', icon: 'task' },// , icon_hovered: DocumentIconSolid },
      { name_en: 'Academic Data Analysis', name_pt: 'Mentoria em Análise de Dados Acadêmicos', icon: 'school' },// , icon_hovered: AcademicCapIconSolid },
    ];

    const handleLogin = () => {
      bus.emit('handleLogin');
    };

    const handlePortfolio = () => {
      router.push('/portfolio');
    };

    return {
      //user,
      slide: ref('card_1'),
      getImageUrl,
      handleLogin,
      handlePortfolio,
      t,
      locale,
      skills,
      goto,
    };
  },
};
</script>
<style lang="scss">
.welcome {
  line-height: 150%;
  letter-spacing: 0.8px;
}
.main-text {
  // color: #000;
  font-weight: 600;
  line-height: 135%;
}
.desc-text {
  // color: var(--neutral-colors-300, #525b67);
  font-style: normal;
  font-weight: 400;
  line-height: 160%; /* 28.8px */
}

/* Classes personalizadas */
.group {
  position: relative;
  display: inline-block;
}

.bg-effect {
  transform: translateX(-100%) scale(1);
  border-radius: 50%;
}

.group:hover .bg-effect {
  transform: translateX(0) scale(1.5);
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.inset-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.overflow-hidden {
  overflow: hidden;
}

.rounded-md {
  border-radius: 0.375rem;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.bg-blue-500 {
  background-color: #3b82f6;
}

.transition-all {
  transition: all 0.5s;
}

.duration-500 {
  transition-duration: 0.5s;
}

.z-10 {
  z-index: 10;
}

.hero-section {
  background-image: url('path-to-your-background-image.jpg');
  background-size: cover;
  background-position: center;
}

.section-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  scroll-margin-top: 100px; /* altura do header */
}
</style>
