<template>
  <!-- <section class="w-full lg:h-auto p-1">
    <section
      class="flex md:flex-row lg:flex-row md:flex-nowrap lg:flex-nowrap sm:flex-col items-center justify-between gap-3 w-screen mx-auto rounded-xl p-[20px]"
    >
      <div
        class="container flex flex-col gap-5 w-full lg:w-1/2 items-center lg:items-start justify-center mt-10 lg:mt-0"
      >
        <div
          v-motion
          :initial="{
            opacity: 0,
            y: -100,
          }"
          :enter="{
            opacity: 1,
            y: 0,
            transition: {
              delay: 600,
            },
          }"
        >
          <img
            :src="getImageUrl('main_section', $q.dark.isActive, true)"
            alt="Welcome"
            class="object-contain w-[350px] h-auto justify-end"
          />
        </div>
      </div>
      <div
        class="container flex flex-col gap-5 w-full lg:w-1/2 items-center lg:items-start justify-center mt-10 lg:mt-0"
        v-motion
        :initial="{
          opacity: 0,
          y: 100,
        }"
        :enter="{
          opacity: 1,
          y: 0,
          transition: {
            delay: 600,
          },
        }"
      >
        <span
          class="welcome uppercase text-xs lg:text-xl font-thin !font-['League']"
          :class="$q.dark.isActive ? 'text-white' : 'text-[#292321]'"
          >{{ $t('home.welcome') }}
        </span>
        <h1
          class="main-text text-lg lg:text-3xl !font-semibold !font-['Segoe']"
        >
          {{ $t('home.productDesc') }}
        </h1>
        <span class="desc-text text-xs lg:text-lg !font-thin !font-['Segoe']">{{
          $t('home.smallDesc')
        }}</span>
        <div
          class="flex flex-col lg:flex-row lg:flex-nowrap items-center justify-start gap-3 w-full"
        >
          <q-btn
            class="rounded-lg !font-['Segoe'] h-12 px-8 py-2 w-[220px]"
            @click="handleLogin"
            :label="$t('home.loginNow')"
            icon-right="login"
            color="accent"
            no-caps
          />

          <button @click="handlePortfolio" class="group relative">
            <div
              v-if="$q.dark.isActive"
              class="relative z-10 inline-flex h-12 items-center justify-center overflow-hidden rounded-md border-2 bg-transparent px-6 font-medium text-white transition-all duration-300 group-hover:-translate-x-3 group-hover:-translate-y-3 w-[220px]"
            >
              {{ $t('home.portfolio') }}
            </div>
            <div
              v-if="$q.dark.isActive"
              class="absolute inset-0 z-0 h-full w-full rounded-md transition-all duration-300 group-hover:-translate-x-3 group-hover:-translate-y-3 group-hover:[box-shadow:5px_5px_#e5e5e5,10px_10px_#b52ee2,15px_15px_#9900cc]"
            ></div>
            <div
              v-if="!$q.dark.isActive"
              class="relative z-10 inline-flex h-12 items-center justify-center overflow-hidden rounded-md border-2 bg-transparent px-6 font-medium text-black transition-all duration-300 group-hover:-translate-x-3 group-hover:-translate-y-3 w-[220px]"
            >
              {{ $t('home.portfolio') }}
            </div>
            <div
              v-if="!$q.dark.isActive"
              class="absolute inset-0 z-0 h-full w-full rounded-md transition-all duration-300 group-hover:-translate-x-3 group-hover:-translate-y-3 group-hover:[box-shadow:5px_5px_#9900cc,10px_10px_#b52ee2,15px_15px_#e5e5e5]"
            ></div>
          </button>
        </div>
      </div>
    </section>
    <section
      class="flex max-w-full md:flex-row lg:flex-row md:flex-nowrap lg:flex-nowrap sm:flex-col items-center justify-between gap-3 w-screen mx-auto rounded-xl p-[20px]"
      :class="$q.dark.isActive ? 'bg-[--q-dark]' : 'bg-accent'"
    >
      <div
        class="container flex flex-col gap-5 w-full lg:w-1/2 items-center lg:items-start justify-center mt-5 lg:mt-0"
      >
        <div
          class="flex w-full justify-center rounded-xl"
          :class="$q.dark.isActive ? 'bg-inherit' : 'bg-inherit'"
          v-motion
          :initial="{
            opacity: 0,
            x: -100,
          }"
          :visibleOnce="{
            opacity: 1,
            x: 0,
            transition: {
              type: 'spring',
              stiffness: 250,
              damping: 25,
              mass: 0.85,
              delay: 650,
            },
          }"
        >
          <q-card-section>
            <q-carousel
              v-model="slide"
              animated
              padding
              infinite
              autoplay
              class="mx-5 h-[170px]"
              :class="$q.dark.isActive ? 'bg-inherit' : 'bg-inherit'"
            >
              <template
                v-for="(item, idx) in [
                  [
                    'carroussel_1',
                    'carroussel_2',
                    'carroussel_3',
                    'carroussel_4',
                  ],
                  [
                    'carroussel_5',
                    'carroussel_6',
                    'carroussel_7',
                    'carroussel_8',
                  ],
                ]"
                :key="idx"
              >
                <q-carousel-slide
                  :name="`card_${idx}`"
                  class="flex items-center justify-center gap-3"
                >
                  <div
                    class="flex flex-col lg:flex-row flex-nowrap items-center justify-around w-full"
                  >
                    <template v-for="img in item" :key="img">
                      <img
                        class="overflow-hidden"
                        :class="
                          $q.screen.lt.lg
                            ? 'w-[150px] h-120px'
                            : 'w-[150px] h-auto'
                        "
                        :src="getImageUrl(img, true, true)"
                        :alt="`logo ${img}`"
                      />
                    </template>
                  </div>
                </q-carousel-slide>
              </template>
            </q-carousel>
          </q-card-section>
        </div>
      </div>
      <div
        class="container flex flex-col gap-5 w-full lg:w-1/2 items-center lg:items-start justify-center mt-10 lg:mt-0"
      >
        <div
          class="lg:w-[80%]"
          v-motion
          :initial="{
            opacity: 0,
            x: $q.screen.lt.lg ? -300 : 300,
          }"
          :visibleOnce="{
            opacity: 1,
            x: 0,
            transition: {
              type: 'spring',
              stiffness: 250,
              damping: 25,
              mass: 0.85,
              delay: 650,
            },
          }"
        >
          <h1 class="main-text text-lg lg:text-3xl !font-semibold">
            A
            <span
              class="text-primary uppercase"
              :class="$q.dark.isActive ? 'text-primary' : 'text-white'"
              >Habilis</span
            >
            conta com diversas formas para ajudar
            <span
              class="text-primary uppercase"
              :class="$q.dark.isActive ? 'text-primary' : 'text-white'"
              >VOCÊ</span
            >
          </h1>
          <span
            class="text-xs lg:text-md !font-thin"
            :class="$q.dark.isActive ? 'text-white' : 'text-white'"
            >Conheça nossas soluções para o seu trabalho acadêmico.</span
          >
        </div>
      </div>
    </section>
  </section> -->
  <section class="bg-[url('/src/assets/HomeBackground.svg')] bg-no-repeat bg-cover h-[70vh] flex items-center p-4" id="home">
      <div class="section-container bg-white rounded-3xl items-center">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 items-center px-16">
          <div class="text-center lg:text-left order-2 md:order-1">
            <h1 class="text-2xl md:text-4xl text-primary font-semibold mb-2 leading-10 md:leading-[60px]">
              {{ t(`home.title`) }}
            </h1>
            <p class="mb-4">{{ t(`home.description`) }}</p>

            <div class="items-center justify-center lg:justify-start mt-5">
              <q-btn
                push
                icon-right="login"
                :label="!$q.screen.lt.sm ? t(`home.signin_now`) : ''"
                padding="xs lg"
                color="primary"
                @click="handleLogin"
              />
                <button class="inline-flex items-center px-4 text-primary font-semibold rounded-std" @click="handlePortfolio"><i class="far fa-play-circle text-4xl mr-2"></i> <span class="text-sm font-bold">{{ t(`home.portfolio`) }}</span></button>
            </div>
          </div>
          <div class="order-1 md:order-2 px-16">
            <q-img
              class="h-auto"
              :src="getImageUrlLogo('undraw_organizing-trends.svg')"
            />
          </div>
        </div>
      </div>      
    </section>

    <div class="custom-shape-divider-bottom-1742234601" style="margin-top: -70px; overflow: hidden;">
      <svg data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
          <path d="M1200,0H0V120H281.94C572.9,116.24,602.45,3.86,602.45,3.86h0S632,116.24,923,120h277Z" class="shape-fill"></path>
      </svg>
    </div>

    <div id="skills" class="section-container py-16">
      <div class="text-center">
        <h2 class="text-3xl font-bold tracking-tight text-gray-900 mt-8">
          {{ t(`home.how_can_we_help`) }}
        </h2>
        <p class="mt-4 text-lg text-gray-600">
          {{ t(`home.meet_our_solutions`) }}
        </p>
      </div>

      <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        <div
          v-for="skill in skills"
          :key="skill.name_pt"
          class="group bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow p-6 text-center relative"
        >
          <div class="group-hover:opacity-100 transition-opacity duration-300">
            <q-icon :name="skill.icon" color="primary" size="25px" />
            <!-- <component
              :is="skill.icon"
              class="w-12 h-12 mx-auto mb-4 text-primary"
            /> -->
            <h3 class="text-lg font-semibold text-gray-900">{{ locale === 'pt' ? skill.name_pt : skill.name_en }}</h3>
          </div>
        </div>
      </div>
    </div>
</template>
<script>
// import { useUserStore } from 'src/stores/user';
import { inject, ref } from 'vue';
import { getImageUrl } from 'src/utils/image';
import { getImageUrlLogo } from 'src/utils/image';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n'

export default {
  name: 'MainSection',
  // components: { AnatomyButton },
  setup() {
    // const user = useUserStore();
    const bus = inject('bus');
    const router = useRouter();
    const { t, locale } = useI18n()

    const skills = [
      { name_en: 'Exploratory Analysis', name_pt: 'Análise Exploratória', icon: 'share' },// , icon_hovered: ShareIconSolid },
      { name_en: 'Data Visualization', name_pt: 'Visualização de Dados', icon: 'visibility' },// , icon_hovered: EyeIconSolid },
      { name_en: 'Data Story', name_pt: 'Data Story', icon: 'menu_book' },// , icon_hovered: BookOpenIconSolid },
      { name_en: 'Hypothesis Testing', name_pt: 'Testes de Hipóteses', icon: 'chat' },// , icon_hovered: ChatBubbleBottomCenterTextIconSolid },
      { name_en: 'Quantitative Survey Structure', name_pt: 'Delineamento de Pesquisa Quantitativa', icon: 'monitoring' },// , icon_hovered: ChartBarSquareIconSolid },
      { name_en: 'Correlation and Regression', name_pt: 'Correlação e Regressão', icon: 'database' },// , icon_hovered: Square3Stack3DIconSolid },
      { name_en: 'Confidence Intervals', name_pt: 'Intervalos de Confiança', icon: 'data_usage' },// , icon_hovered: ChartPieIconSolid },
      { name_en: 'Survey Methodology', name_pt: 'Metodologia de Pesquisa', icon: 'search' },// , icon_hovered: DocumentMagnifyingGlassIconSolid },
      { name_en: 'Results Interpretation', name_pt: 'Interpretação de Resultados', icon: 'trending_up' },// , icon_hovered: ArrowTrendingUpIconSolid },
      { name_en: 'Scientific Writing', name_pt: 'Escrita Científica', icon: 'science' },// , icon_hovered: BeakerIconSolid },
      { name_en: 'Data Analysis Reports', name_pt: 'Relatórios de Análise', icon: 'task' },// , icon_hovered: DocumentIconSolid },
      { name_en: 'Academic Data Analysis', name_pt: 'Mentoria em Análise de Dados Acadêmicos', icon: 'school' },// , icon_hovered: AcademicCapIconSolid },
    ];

    const handleLogin = () => {
      bus.emit('handleLogin');
    };

    const handlePortfolio = () => {
      router.push('/portfolio');
    };

    return {
      //user,
      slide: ref('card_1'),
      getImageUrl,
      handleLogin,
      handlePortfolio,
      t,
      locale,
      skills,
      getImageUrlLogo,
    };
  },
};
</script>
<style lang="scss">
.welcome {
  line-height: 150%;
  letter-spacing: 0.8px;
}
.main-text {
  // color: #000;
  font-weight: 600;
  line-height: 135%;
}
.desc-text {
  // color: var(--neutral-colors-300, #525b67);
  font-style: normal;
  font-weight: 400;
  line-height: 160%; /* 28.8px */
}

/* Classes personalizadas */
.group {
  position: relative;
  display: inline-block;
}

.bg-effect {
  transform: translateX(-100%) scale(1);
  border-radius: 50%;
}

.group:hover .bg-effect {
  transform: translateX(0) scale(1.5);
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.inset-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.overflow-hidden {
  overflow: hidden;
}

.rounded-md {
  border-radius: 0.375rem;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.bg-blue-500 {
  background-color: #3b82f6;
}

.transition-all {
  transition: all 0.5s;
}

.duration-500 {
  transition-duration: 0.5s;
}

.z-10 {
  z-index: 10;
}

.hero-section {
  background-image: url('path-to-your-background-image.jpg');
  background-size: cover;
  background-position: center;
}

.section-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  scroll-margin-top: 100px; /* altura do header */
}
</style>
